// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserData {

 String get email;@JsonKey(name: 'is_after_test') bool get afterTest;@JsonKey(name: 'last_pronunciation') LastCourse? get lastPronunciation;@JsonKey(name: 'last_conversation') LastCourse? get lastConversation;@JsonKey(name: 'last_listening') LastCourse? get lastListening;@JsonKey(name: 'last_speaking') LastCourse? get lastSpeaking;
/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserDataCopyWith<UserData> get copyWith => _$UserDataCopyWithImpl<UserData>(this as UserData, _$identity);

  /// Serializes this UserData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserData&&(identical(other.email, email) || other.email == email)&&(identical(other.afterTest, afterTest) || other.afterTest == afterTest)&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,afterTest,lastPronunciation,lastConversation,lastListening,lastSpeaking);

@override
String toString() {
  return 'UserData(email: $email, afterTest: $afterTest, lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking)';
}


}

/// @nodoc
abstract mixin class $UserDataCopyWith<$Res>  {
  factory $UserDataCopyWith(UserData value, $Res Function(UserData) _then) = _$UserDataCopyWithImpl;
@useResult
$Res call({
 String email,@JsonKey(name: 'is_after_test') bool afterTest,@JsonKey(name: 'last_pronunciation') LastCourse? lastPronunciation,@JsonKey(name: 'last_conversation') LastCourse? lastConversation,@JsonKey(name: 'last_listening') LastCourse? lastListening,@JsonKey(name: 'last_speaking') LastCourse? lastSpeaking
});


$LastCourseCopyWith<$Res>? get lastPronunciation;$LastCourseCopyWith<$Res>? get lastConversation;$LastCourseCopyWith<$Res>? get lastListening;$LastCourseCopyWith<$Res>? get lastSpeaking;

}
/// @nodoc
class _$UserDataCopyWithImpl<$Res>
    implements $UserDataCopyWith<$Res> {
  _$UserDataCopyWithImpl(this._self, this._then);

  final UserData _self;
  final $Res Function(UserData) _then;

/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? afterTest = null,Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,afterTest: null == afterTest ? _self.afterTest : afterTest // ignore: cast_nullable_to_non_nullable
as bool,lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,
  ));
}
/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}
}


/// Adds pattern-matching-related methods to [UserData].
extension UserDataPatterns on UserData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserData value)  $default,){
final _that = this;
switch (_that) {
case _UserData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserData value)?  $default,){
final _that = this;
switch (_that) {
case _UserData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String email, @JsonKey(name: 'is_after_test')  bool afterTest, @JsonKey(name: 'last_pronunciation')  LastCourse? lastPronunciation, @JsonKey(name: 'last_conversation')  LastCourse? lastConversation, @JsonKey(name: 'last_listening')  LastCourse? lastListening, @JsonKey(name: 'last_speaking')  LastCourse? lastSpeaking)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserData() when $default != null:
return $default(_that.email,_that.afterTest,_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String email, @JsonKey(name: 'is_after_test')  bool afterTest, @JsonKey(name: 'last_pronunciation')  LastCourse? lastPronunciation, @JsonKey(name: 'last_conversation')  LastCourse? lastConversation, @JsonKey(name: 'last_listening')  LastCourse? lastListening, @JsonKey(name: 'last_speaking')  LastCourse? lastSpeaking)  $default,) {final _that = this;
switch (_that) {
case _UserData():
return $default(_that.email,_that.afterTest,_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String email, @JsonKey(name: 'is_after_test')  bool afterTest, @JsonKey(name: 'last_pronunciation')  LastCourse? lastPronunciation, @JsonKey(name: 'last_conversation')  LastCourse? lastConversation, @JsonKey(name: 'last_listening')  LastCourse? lastListening, @JsonKey(name: 'last_speaking')  LastCourse? lastSpeaking)?  $default,) {final _that = this;
switch (_that) {
case _UserData() when $default != null:
return $default(_that.email,_that.afterTest,_that.lastPronunciation,_that.lastConversation,_that.lastListening,_that.lastSpeaking);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserData implements UserData {
  const _UserData({required this.email, @JsonKey(name: 'is_after_test') this.afterTest = false, @JsonKey(name: 'last_pronunciation') this.lastPronunciation, @JsonKey(name: 'last_conversation') this.lastConversation, @JsonKey(name: 'last_listening') this.lastListening, @JsonKey(name: 'last_speaking') this.lastSpeaking});
  factory _UserData.fromJson(Map<String, dynamic> json) => _$UserDataFromJson(json);

@override final  String email;
@override@JsonKey(name: 'is_after_test') final  bool afterTest;
@override@JsonKey(name: 'last_pronunciation') final  LastCourse? lastPronunciation;
@override@JsonKey(name: 'last_conversation') final  LastCourse? lastConversation;
@override@JsonKey(name: 'last_listening') final  LastCourse? lastListening;
@override@JsonKey(name: 'last_speaking') final  LastCourse? lastSpeaking;

/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserDataCopyWith<_UserData> get copyWith => __$UserDataCopyWithImpl<_UserData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserData&&(identical(other.email, email) || other.email == email)&&(identical(other.afterTest, afterTest) || other.afterTest == afterTest)&&(identical(other.lastPronunciation, lastPronunciation) || other.lastPronunciation == lastPronunciation)&&(identical(other.lastConversation, lastConversation) || other.lastConversation == lastConversation)&&(identical(other.lastListening, lastListening) || other.lastListening == lastListening)&&(identical(other.lastSpeaking, lastSpeaking) || other.lastSpeaking == lastSpeaking));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,email,afterTest,lastPronunciation,lastConversation,lastListening,lastSpeaking);

@override
String toString() {
  return 'UserData(email: $email, afterTest: $afterTest, lastPronunciation: $lastPronunciation, lastConversation: $lastConversation, lastListening: $lastListening, lastSpeaking: $lastSpeaking)';
}


}

/// @nodoc
abstract mixin class _$UserDataCopyWith<$Res> implements $UserDataCopyWith<$Res> {
  factory _$UserDataCopyWith(_UserData value, $Res Function(_UserData) _then) = __$UserDataCopyWithImpl;
@override @useResult
$Res call({
 String email,@JsonKey(name: 'is_after_test') bool afterTest,@JsonKey(name: 'last_pronunciation') LastCourse? lastPronunciation,@JsonKey(name: 'last_conversation') LastCourse? lastConversation,@JsonKey(name: 'last_listening') LastCourse? lastListening,@JsonKey(name: 'last_speaking') LastCourse? lastSpeaking
});


@override $LastCourseCopyWith<$Res>? get lastPronunciation;@override $LastCourseCopyWith<$Res>? get lastConversation;@override $LastCourseCopyWith<$Res>? get lastListening;@override $LastCourseCopyWith<$Res>? get lastSpeaking;

}
/// @nodoc
class __$UserDataCopyWithImpl<$Res>
    implements _$UserDataCopyWith<$Res> {
  __$UserDataCopyWithImpl(this._self, this._then);

  final _UserData _self;
  final $Res Function(_UserData) _then;

/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? afterTest = null,Object? lastPronunciation = freezed,Object? lastConversation = freezed,Object? lastListening = freezed,Object? lastSpeaking = freezed,}) {
  return _then(_UserData(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,afterTest: null == afterTest ? _self.afterTest : afterTest // ignore: cast_nullable_to_non_nullable
as bool,lastPronunciation: freezed == lastPronunciation ? _self.lastPronunciation : lastPronunciation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastConversation: freezed == lastConversation ? _self.lastConversation : lastConversation // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastListening: freezed == lastListening ? _self.lastListening : lastListening // ignore: cast_nullable_to_non_nullable
as LastCourse?,lastSpeaking: freezed == lastSpeaking ? _self.lastSpeaking : lastSpeaking // ignore: cast_nullable_to_non_nullable
as LastCourse?,
  ));
}

/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastPronunciation {
    if (_self.lastPronunciation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastPronunciation!, (value) {
    return _then(_self.copyWith(lastPronunciation: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastConversation {
    if (_self.lastConversation == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastConversation!, (value) {
    return _then(_self.copyWith(lastConversation: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastListening {
    if (_self.lastListening == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastListening!, (value) {
    return _then(_self.copyWith(lastListening: value));
  });
}/// Create a copy of UserData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res>? get lastSpeaking {
    if (_self.lastSpeaking == null) {
    return null;
  }

  return $LastCourseCopyWith<$Res>(_self.lastSpeaking!, (value) {
    return _then(_self.copyWith(lastSpeaking: value));
  });
}
}


/// @nodoc
mixin _$LastCourse {

 DateTime get accessTime; String get level; int get chapter; SectionType get section; String get path; SpeakingStage get speakingStage; int? get partOrder; int? get subpartOrder;
/// Create a copy of LastCourse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LastCourseCopyWith<LastCourse> get copyWith => _$LastCourseCopyWithImpl<LastCourse>(this as LastCourse, _$identity);

  /// Serializes this LastCourse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LastCourse&&(identical(other.accessTime, accessTime) || other.accessTime == accessTime)&&(identical(other.level, level) || other.level == level)&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.section, section) || other.section == section)&&(identical(other.path, path) || other.path == path)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessTime,level,chapter,section,path,speakingStage,partOrder,subpartOrder);

@override
String toString() {
  return 'LastCourse(accessTime: $accessTime, level: $level, chapter: $chapter, section: $section, path: $path, speakingStage: $speakingStage, partOrder: $partOrder, subpartOrder: $subpartOrder)';
}


}

/// @nodoc
abstract mixin class $LastCourseCopyWith<$Res>  {
  factory $LastCourseCopyWith(LastCourse value, $Res Function(LastCourse) _then) = _$LastCourseCopyWithImpl;
@useResult
$Res call({
 DateTime accessTime, String level, int chapter, SectionType section, String path, SpeakingStage speakingStage, int? partOrder, int? subpartOrder
});




}
/// @nodoc
class _$LastCourseCopyWithImpl<$Res>
    implements $LastCourseCopyWith<$Res> {
  _$LastCourseCopyWithImpl(this._self, this._then);

  final LastCourse _self;
  final $Res Function(LastCourse) _then;

/// Create a copy of LastCourse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accessTime = null,Object? level = null,Object? chapter = null,Object? section = null,Object? path = null,Object? speakingStage = null,Object? partOrder = freezed,Object? subpartOrder = freezed,}) {
  return _then(_self.copyWith(
accessTime: null == accessTime ? _self.accessTime : accessTime // ignore: cast_nullable_to_non_nullable
as DateTime,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as SectionType,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,speakingStage: null == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [LastCourse].
extension LastCoursePatterns on LastCourse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LastCourse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LastCourse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LastCourse value)  $default,){
final _that = this;
switch (_that) {
case _LastCourse():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LastCourse value)?  $default,){
final _that = this;
switch (_that) {
case _LastCourse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime accessTime,  String level,  int chapter,  SectionType section,  String path,  SpeakingStage speakingStage,  int? partOrder,  int? subpartOrder)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LastCourse() when $default != null:
return $default(_that.accessTime,_that.level,_that.chapter,_that.section,_that.path,_that.speakingStage,_that.partOrder,_that.subpartOrder);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime accessTime,  String level,  int chapter,  SectionType section,  String path,  SpeakingStage speakingStage,  int? partOrder,  int? subpartOrder)  $default,) {final _that = this;
switch (_that) {
case _LastCourse():
return $default(_that.accessTime,_that.level,_that.chapter,_that.section,_that.path,_that.speakingStage,_that.partOrder,_that.subpartOrder);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime accessTime,  String level,  int chapter,  SectionType section,  String path,  SpeakingStage speakingStage,  int? partOrder,  int? subpartOrder)?  $default,) {final _that = this;
switch (_that) {
case _LastCourse() when $default != null:
return $default(_that.accessTime,_that.level,_that.chapter,_that.section,_that.path,_that.speakingStage,_that.partOrder,_that.subpartOrder);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LastCourse implements LastCourse {
  const _LastCourse({required this.accessTime, required this.level, required this.chapter, required this.section, required this.path, this.speakingStage = SpeakingStage.stage1, this.partOrder, this.subpartOrder});
  factory _LastCourse.fromJson(Map<String, dynamic> json) => _$LastCourseFromJson(json);

@override final  DateTime accessTime;
@override final  String level;
@override final  int chapter;
@override final  SectionType section;
@override final  String path;
@override@JsonKey() final  SpeakingStage speakingStage;
@override final  int? partOrder;
@override final  int? subpartOrder;

/// Create a copy of LastCourse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LastCourseCopyWith<_LastCourse> get copyWith => __$LastCourseCopyWithImpl<_LastCourse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LastCourseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LastCourse&&(identical(other.accessTime, accessTime) || other.accessTime == accessTime)&&(identical(other.level, level) || other.level == level)&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.section, section) || other.section == section)&&(identical(other.path, path) || other.path == path)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accessTime,level,chapter,section,path,speakingStage,partOrder,subpartOrder);

@override
String toString() {
  return 'LastCourse(accessTime: $accessTime, level: $level, chapter: $chapter, section: $section, path: $path, speakingStage: $speakingStage, partOrder: $partOrder, subpartOrder: $subpartOrder)';
}


}

/// @nodoc
abstract mixin class _$LastCourseCopyWith<$Res> implements $LastCourseCopyWith<$Res> {
  factory _$LastCourseCopyWith(_LastCourse value, $Res Function(_LastCourse) _then) = __$LastCourseCopyWithImpl;
@override @useResult
$Res call({
 DateTime accessTime, String level, int chapter, SectionType section, String path, SpeakingStage speakingStage, int? partOrder, int? subpartOrder
});




}
/// @nodoc
class __$LastCourseCopyWithImpl<$Res>
    implements _$LastCourseCopyWith<$Res> {
  __$LastCourseCopyWithImpl(this._self, this._then);

  final _LastCourse _self;
  final $Res Function(_LastCourse) _then;

/// Create a copy of LastCourse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accessTime = null,Object? level = null,Object? chapter = null,Object? section = null,Object? path = null,Object? speakingStage = null,Object? partOrder = freezed,Object? subpartOrder = freezed,}) {
  return _then(_LastCourse(
accessTime: null == accessTime ? _self.accessTime : accessTime // ignore: cast_nullable_to_non_nullable
as DateTime,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,section: null == section ? _self.section : section // ignore: cast_nullable_to_non_nullable
as SectionType,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,speakingStage: null == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$LastCourseInfo {

 LastCourse get info; dynamic get data;
/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LastCourseInfoCopyWith<LastCourseInfo> get copyWith => _$LastCourseInfoCopyWithImpl<LastCourseInfo>(this as LastCourseInfo, _$identity);

  /// Serializes this LastCourseInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LastCourseInfo&&(identical(other.info, info) || other.info == info)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,info,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'LastCourseInfo(info: $info, data: $data)';
}


}

/// @nodoc
abstract mixin class $LastCourseInfoCopyWith<$Res>  {
  factory $LastCourseInfoCopyWith(LastCourseInfo value, $Res Function(LastCourseInfo) _then) = _$LastCourseInfoCopyWithImpl;
@useResult
$Res call({
 LastCourse info, dynamic data
});


$LastCourseCopyWith<$Res> get info;

}
/// @nodoc
class _$LastCourseInfoCopyWithImpl<$Res>
    implements $LastCourseInfoCopyWith<$Res> {
  _$LastCourseInfoCopyWithImpl(this._self, this._then);

  final LastCourseInfo _self;
  final $Res Function(LastCourseInfo) _then;

/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? info = null,Object? data = freezed,}) {
  return _then(_self.copyWith(
info: null == info ? _self.info : info // ignore: cast_nullable_to_non_nullable
as LastCourse,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}
/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res> get info {
  
  return $LastCourseCopyWith<$Res>(_self.info, (value) {
    return _then(_self.copyWith(info: value));
  });
}
}


/// Adds pattern-matching-related methods to [LastCourseInfo].
extension LastCourseInfoPatterns on LastCourseInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LastCourseInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LastCourseInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LastCourseInfo value)  $default,){
final _that = this;
switch (_that) {
case _LastCourseInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LastCourseInfo value)?  $default,){
final _that = this;
switch (_that) {
case _LastCourseInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( LastCourse info,  dynamic data)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LastCourseInfo() when $default != null:
return $default(_that.info,_that.data);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( LastCourse info,  dynamic data)  $default,) {final _that = this;
switch (_that) {
case _LastCourseInfo():
return $default(_that.info,_that.data);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( LastCourse info,  dynamic data)?  $default,) {final _that = this;
switch (_that) {
case _LastCourseInfo() when $default != null:
return $default(_that.info,_that.data);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LastCourseInfo implements LastCourseInfo {
  const _LastCourseInfo({required this.info, this.data});
  factory _LastCourseInfo.fromJson(Map<String, dynamic> json) => _$LastCourseInfoFromJson(json);

@override final  LastCourse info;
@override final  dynamic data;

/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LastCourseInfoCopyWith<_LastCourseInfo> get copyWith => __$LastCourseInfoCopyWithImpl<_LastCourseInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LastCourseInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LastCourseInfo&&(identical(other.info, info) || other.info == info)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,info,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'LastCourseInfo(info: $info, data: $data)';
}


}

/// @nodoc
abstract mixin class _$LastCourseInfoCopyWith<$Res> implements $LastCourseInfoCopyWith<$Res> {
  factory _$LastCourseInfoCopyWith(_LastCourseInfo value, $Res Function(_LastCourseInfo) _then) = __$LastCourseInfoCopyWithImpl;
@override @useResult
$Res call({
 LastCourse info, dynamic data
});


@override $LastCourseCopyWith<$Res> get info;

}
/// @nodoc
class __$LastCourseInfoCopyWithImpl<$Res>
    implements _$LastCourseInfoCopyWith<$Res> {
  __$LastCourseInfoCopyWithImpl(this._self, this._then);

  final _LastCourseInfo _self;
  final $Res Function(_LastCourseInfo) _then;

/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? info = null,Object? data = freezed,}) {
  return _then(_LastCourseInfo(
info: null == info ? _self.info : info // ignore: cast_nullable_to_non_nullable
as LastCourse,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}

/// Create a copy of LastCourseInfo
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LastCourseCopyWith<$Res> get info {
  
  return $LastCourseCopyWith<$Res>(_self.info, (value) {
    return _then(_self.copyWith(info: value));
  });
}
}


/// @nodoc
mixin _$LessonResult {

 int? get partOrder; int? get subpartOrder; SpeakingStage? get speakingStage; int get contentOrder; String get path; Map<String, dynamic> get result;
/// Create a copy of LessonResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LessonResultCopyWith<LessonResult> get copyWith => _$LessonResultCopyWithImpl<LessonResult>(this as LessonResult, _$identity);

  /// Serializes this LessonResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LessonResult&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.contentOrder, contentOrder) || other.contentOrder == contentOrder)&&(identical(other.path, path) || other.path == path)&&const DeepCollectionEquality().equals(other.result, result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,partOrder,subpartOrder,speakingStage,contentOrder,path,const DeepCollectionEquality().hash(result));

@override
String toString() {
  return 'LessonResult(partOrder: $partOrder, subpartOrder: $subpartOrder, speakingStage: $speakingStage, contentOrder: $contentOrder, path: $path, result: $result)';
}


}

/// @nodoc
abstract mixin class $LessonResultCopyWith<$Res>  {
  factory $LessonResultCopyWith(LessonResult value, $Res Function(LessonResult) _then) = _$LessonResultCopyWithImpl;
@useResult
$Res call({
 int? partOrder, int? subpartOrder, SpeakingStage? speakingStage, int contentOrder, String path, Map<String, dynamic> result
});




}
/// @nodoc
class _$LessonResultCopyWithImpl<$Res>
    implements $LessonResultCopyWith<$Res> {
  _$LessonResultCopyWithImpl(this._self, this._then);

  final LessonResult _self;
  final $Res Function(LessonResult) _then;

/// Create a copy of LessonResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? partOrder = freezed,Object? subpartOrder = freezed,Object? speakingStage = freezed,Object? contentOrder = null,Object? path = null,Object? result = null,}) {
  return _then(_self.copyWith(
partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,speakingStage: freezed == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage?,contentOrder: null == contentOrder ? _self.contentOrder : contentOrder // ignore: cast_nullable_to_non_nullable
as int,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [LessonResult].
extension LessonResultPatterns on LessonResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LessonResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LessonResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LessonResult value)  $default,){
final _that = this;
switch (_that) {
case _LessonResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LessonResult value)?  $default,){
final _that = this;
switch (_that) {
case _LessonResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? partOrder,  int? subpartOrder,  SpeakingStage? speakingStage,  int contentOrder,  String path,  Map<String, dynamic> result)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LessonResult() when $default != null:
return $default(_that.partOrder,_that.subpartOrder,_that.speakingStage,_that.contentOrder,_that.path,_that.result);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? partOrder,  int? subpartOrder,  SpeakingStage? speakingStage,  int contentOrder,  String path,  Map<String, dynamic> result)  $default,) {final _that = this;
switch (_that) {
case _LessonResult():
return $default(_that.partOrder,_that.subpartOrder,_that.speakingStage,_that.contentOrder,_that.path,_that.result);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? partOrder,  int? subpartOrder,  SpeakingStage? speakingStage,  int contentOrder,  String path,  Map<String, dynamic> result)?  $default,) {final _that = this;
switch (_that) {
case _LessonResult() when $default != null:
return $default(_that.partOrder,_that.subpartOrder,_that.speakingStage,_that.contentOrder,_that.path,_that.result);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LessonResult implements LessonResult {
  const _LessonResult({this.partOrder, this.subpartOrder, this.speakingStage, required this.contentOrder, required this.path, required final  Map<String, dynamic> result}): _result = result;
  factory _LessonResult.fromJson(Map<String, dynamic> json) => _$LessonResultFromJson(json);

@override final  int? partOrder;
@override final  int? subpartOrder;
@override final  SpeakingStage? speakingStage;
@override final  int contentOrder;
@override final  String path;
 final  Map<String, dynamic> _result;
@override Map<String, dynamic> get result {
  if (_result is EqualUnmodifiableMapView) return _result;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_result);
}


/// Create a copy of LessonResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LessonResultCopyWith<_LessonResult> get copyWith => __$LessonResultCopyWithImpl<_LessonResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LessonResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LessonResult&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder)&&(identical(other.speakingStage, speakingStage) || other.speakingStage == speakingStage)&&(identical(other.contentOrder, contentOrder) || other.contentOrder == contentOrder)&&(identical(other.path, path) || other.path == path)&&const DeepCollectionEquality().equals(other._result, _result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,partOrder,subpartOrder,speakingStage,contentOrder,path,const DeepCollectionEquality().hash(_result));

@override
String toString() {
  return 'LessonResult(partOrder: $partOrder, subpartOrder: $subpartOrder, speakingStage: $speakingStage, contentOrder: $contentOrder, path: $path, result: $result)';
}


}

/// @nodoc
abstract mixin class _$LessonResultCopyWith<$Res> implements $LessonResultCopyWith<$Res> {
  factory _$LessonResultCopyWith(_LessonResult value, $Res Function(_LessonResult) _then) = __$LessonResultCopyWithImpl;
@override @useResult
$Res call({
 int? partOrder, int? subpartOrder, SpeakingStage? speakingStage, int contentOrder, String path, Map<String, dynamic> result
});




}
/// @nodoc
class __$LessonResultCopyWithImpl<$Res>
    implements _$LessonResultCopyWith<$Res> {
  __$LessonResultCopyWithImpl(this._self, this._then);

  final _LessonResult _self;
  final $Res Function(_LessonResult) _then;

/// Create a copy of LessonResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? partOrder = freezed,Object? subpartOrder = freezed,Object? speakingStage = freezed,Object? contentOrder = null,Object? path = null,Object? result = null,}) {
  return _then(_LessonResult(
partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,speakingStage: freezed == speakingStage ? _self.speakingStage : speakingStage // ignore: cast_nullable_to_non_nullable
as SpeakingStage?,contentOrder: null == contentOrder ? _self.contentOrder : contentOrder // ignore: cast_nullable_to_non_nullable
as int,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,result: null == result ? _self._result : result // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}


/// @nodoc
mixin _$PronunciationScore {

 double get accuracyScore; double get fluencyScore; double get prosodyScore; double get completenessScore; double get pronScore;
/// Create a copy of PronunciationScore
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationScoreCopyWith<PronunciationScore> get copyWith => _$PronunciationScoreCopyWithImpl<PronunciationScore>(this as PronunciationScore, _$identity);

  /// Serializes this PronunciationScore to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore);

@override
String toString() {
  return 'PronunciationScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore)';
}


}

/// @nodoc
abstract mixin class $PronunciationScoreCopyWith<$Res>  {
  factory $PronunciationScoreCopyWith(PronunciationScore value, $Res Function(PronunciationScore) _then) = _$PronunciationScoreCopyWithImpl;
@useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore
});




}
/// @nodoc
class _$PronunciationScoreCopyWithImpl<$Res>
    implements $PronunciationScoreCopyWith<$Res> {
  _$PronunciationScoreCopyWithImpl(this._self, this._then);

  final PronunciationScore _self;
  final $Res Function(PronunciationScore) _then;

/// Create a copy of PronunciationScore
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,}) {
  return _then(_self.copyWith(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationScore].
extension PronunciationScorePatterns on PronunciationScore {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationScore value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationScore() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationScore value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationScore():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationScore value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationScore() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore)  $default,) {final _that = this;
switch (_that) {
case _PronunciationScore():
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationScore implements PronunciationScore {
  const _PronunciationScore({this.accuracyScore = 0, this.fluencyScore = 0, this.prosodyScore = 0, this.completenessScore = 0, this.pronScore = 0});
  factory _PronunciationScore.fromJson(Map<String, dynamic> json) => _$PronunciationScoreFromJson(json);

@override@JsonKey() final  double accuracyScore;
@override@JsonKey() final  double fluencyScore;
@override@JsonKey() final  double prosodyScore;
@override@JsonKey() final  double completenessScore;
@override@JsonKey() final  double pronScore;

/// Create a copy of PronunciationScore
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationScoreCopyWith<_PronunciationScore> get copyWith => __$PronunciationScoreCopyWithImpl<_PronunciationScore>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationScoreToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore);

@override
String toString() {
  return 'PronunciationScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore)';
}


}

/// @nodoc
abstract mixin class _$PronunciationScoreCopyWith<$Res> implements $PronunciationScoreCopyWith<$Res> {
  factory _$PronunciationScoreCopyWith(_PronunciationScore value, $Res Function(_PronunciationScore) _then) = __$PronunciationScoreCopyWithImpl;
@override @useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore
});




}
/// @nodoc
class __$PronunciationScoreCopyWithImpl<$Res>
    implements _$PronunciationScoreCopyWith<$Res> {
  __$PronunciationScoreCopyWithImpl(this._self, this._then);

  final _PronunciationScore _self;
  final $Res Function(_PronunciationScore) _then;

/// Create a copy of PronunciationScore
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,}) {
  return _then(_PronunciationScore(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$PronunciationScoreParams {

 String get level; String get chapter; int? get partOrder; int? get subpartOrder;
/// Create a copy of PronunciationScoreParams
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationScoreParamsCopyWith<PronunciationScoreParams> get copyWith => _$PronunciationScoreParamsCopyWithImpl<PronunciationScoreParams>(this as PronunciationScoreParams, _$identity);

  /// Serializes this PronunciationScoreParams to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationScoreParams&&(identical(other.level, level) || other.level == level)&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,level,chapter,partOrder,subpartOrder);

@override
String toString() {
  return 'PronunciationScoreParams(level: $level, chapter: $chapter, partOrder: $partOrder, subpartOrder: $subpartOrder)';
}


}

/// @nodoc
abstract mixin class $PronunciationScoreParamsCopyWith<$Res>  {
  factory $PronunciationScoreParamsCopyWith(PronunciationScoreParams value, $Res Function(PronunciationScoreParams) _then) = _$PronunciationScoreParamsCopyWithImpl;
@useResult
$Res call({
 String level, String chapter, int? partOrder, int? subpartOrder
});




}
/// @nodoc
class _$PronunciationScoreParamsCopyWithImpl<$Res>
    implements $PronunciationScoreParamsCopyWith<$Res> {
  _$PronunciationScoreParamsCopyWithImpl(this._self, this._then);

  final PronunciationScoreParams _self;
  final $Res Function(PronunciationScoreParams) _then;

/// Create a copy of PronunciationScoreParams
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? level = null,Object? chapter = null,Object? partOrder = freezed,Object? subpartOrder = freezed,}) {
  return _then(_self.copyWith(
level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as String,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationScoreParams].
extension PronunciationScoreParamsPatterns on PronunciationScoreParams {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationScoreParams value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationScoreParams() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationScoreParams value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationScoreParams():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationScoreParams value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationScoreParams() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String level,  String chapter,  int? partOrder,  int? subpartOrder)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationScoreParams() when $default != null:
return $default(_that.level,_that.chapter,_that.partOrder,_that.subpartOrder);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String level,  String chapter,  int? partOrder,  int? subpartOrder)  $default,) {final _that = this;
switch (_that) {
case _PronunciationScoreParams():
return $default(_that.level,_that.chapter,_that.partOrder,_that.subpartOrder);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String level,  String chapter,  int? partOrder,  int? subpartOrder)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationScoreParams() when $default != null:
return $default(_that.level,_that.chapter,_that.partOrder,_that.subpartOrder);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationScoreParams implements PronunciationScoreParams {
  const _PronunciationScoreParams({required this.level, required this.chapter, this.partOrder, this.subpartOrder});
  factory _PronunciationScoreParams.fromJson(Map<String, dynamic> json) => _$PronunciationScoreParamsFromJson(json);

@override final  String level;
@override final  String chapter;
@override final  int? partOrder;
@override final  int? subpartOrder;

/// Create a copy of PronunciationScoreParams
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationScoreParamsCopyWith<_PronunciationScoreParams> get copyWith => __$PronunciationScoreParamsCopyWithImpl<_PronunciationScoreParams>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationScoreParamsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationScoreParams&&(identical(other.level, level) || other.level == level)&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,level,chapter,partOrder,subpartOrder);

@override
String toString() {
  return 'PronunciationScoreParams(level: $level, chapter: $chapter, partOrder: $partOrder, subpartOrder: $subpartOrder)';
}


}

/// @nodoc
abstract mixin class _$PronunciationScoreParamsCopyWith<$Res> implements $PronunciationScoreParamsCopyWith<$Res> {
  factory _$PronunciationScoreParamsCopyWith(_PronunciationScoreParams value, $Res Function(_PronunciationScoreParams) _then) = __$PronunciationScoreParamsCopyWithImpl;
@override @useResult
$Res call({
 String level, String chapter, int? partOrder, int? subpartOrder
});




}
/// @nodoc
class __$PronunciationScoreParamsCopyWithImpl<$Res>
    implements _$PronunciationScoreParamsCopyWith<$Res> {
  __$PronunciationScoreParamsCopyWithImpl(this._self, this._then);

  final _PronunciationScoreParams _self;
  final $Res Function(_PronunciationScoreParams) _then;

/// Create a copy of PronunciationScoreParams
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? level = null,Object? chapter = null,Object? partOrder = freezed,Object? subpartOrder = freezed,}) {
  return _then(_PronunciationScoreParams(
level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as String,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$PronunciationAgregateScore {

 double get accuracyScore; double get fluencyScore; double get prosodyScore; double get completenessScore; double get pronScore; int get dataCount;
/// Create a copy of PronunciationAgregateScore
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationAgregateScoreCopyWith<PronunciationAgregateScore> get copyWith => _$PronunciationAgregateScoreCopyWithImpl<PronunciationAgregateScore>(this as PronunciationAgregateScore, _$identity);

  /// Serializes this PronunciationAgregateScore to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationAgregateScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&(identical(other.dataCount, dataCount) || other.dataCount == dataCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,dataCount);

@override
String toString() {
  return 'PronunciationAgregateScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, dataCount: $dataCount)';
}


}

/// @nodoc
abstract mixin class $PronunciationAgregateScoreCopyWith<$Res>  {
  factory $PronunciationAgregateScoreCopyWith(PronunciationAgregateScore value, $Res Function(PronunciationAgregateScore) _then) = _$PronunciationAgregateScoreCopyWithImpl;
@useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, int dataCount
});




}
/// @nodoc
class _$PronunciationAgregateScoreCopyWithImpl<$Res>
    implements $PronunciationAgregateScoreCopyWith<$Res> {
  _$PronunciationAgregateScoreCopyWithImpl(this._self, this._then);

  final PronunciationAgregateScore _self;
  final $Res Function(PronunciationAgregateScore) _then;

/// Create a copy of PronunciationAgregateScore
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? dataCount = null,}) {
  return _then(_self.copyWith(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,dataCount: null == dataCount ? _self.dataCount : dataCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationAgregateScore].
extension PronunciationAgregateScorePatterns on PronunciationAgregateScore {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationAgregateScore value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationAgregateScore() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationAgregateScore value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationAgregateScore():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationAgregateScore value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationAgregateScore() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationAgregateScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)  $default,) {final _that = this;
switch (_that) {
case _PronunciationAgregateScore():
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationAgregateScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationAgregateScore implements PronunciationAgregateScore {
  const _PronunciationAgregateScore({this.accuracyScore = 0, this.fluencyScore = 0, this.prosodyScore = 0, this.completenessScore = 0, this.pronScore = 0, this.dataCount = 0});
  factory _PronunciationAgregateScore.fromJson(Map<String, dynamic> json) => _$PronunciationAgregateScoreFromJson(json);

@override@JsonKey() final  double accuracyScore;
@override@JsonKey() final  double fluencyScore;
@override@JsonKey() final  double prosodyScore;
@override@JsonKey() final  double completenessScore;
@override@JsonKey() final  double pronScore;
@override@JsonKey() final  int dataCount;

/// Create a copy of PronunciationAgregateScore
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationAgregateScoreCopyWith<_PronunciationAgregateScore> get copyWith => __$PronunciationAgregateScoreCopyWithImpl<_PronunciationAgregateScore>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationAgregateScoreToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationAgregateScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&(identical(other.dataCount, dataCount) || other.dataCount == dataCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,dataCount);

@override
String toString() {
  return 'PronunciationAgregateScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, dataCount: $dataCount)';
}


}

/// @nodoc
abstract mixin class _$PronunciationAgregateScoreCopyWith<$Res> implements $PronunciationAgregateScoreCopyWith<$Res> {
  factory _$PronunciationAgregateScoreCopyWith(_PronunciationAgregateScore value, $Res Function(_PronunciationAgregateScore) _then) = __$PronunciationAgregateScoreCopyWithImpl;
@override @useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, int dataCount
});




}
/// @nodoc
class __$PronunciationAgregateScoreCopyWithImpl<$Res>
    implements _$PronunciationAgregateScoreCopyWith<$Res> {
  __$PronunciationAgregateScoreCopyWithImpl(this._self, this._then);

  final _PronunciationAgregateScore _self;
  final $Res Function(_PronunciationAgregateScore) _then;

/// Create a copy of PronunciationAgregateScore
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? dataCount = null,}) {
  return _then(_PronunciationAgregateScore(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,dataCount: null == dataCount ? _self.dataCount : dataCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$SpeakingAgregateScore {

 double get accuracyScore; double get fluencyScore; double get prosodyScore; double get completenessScore; double get pronScore; int get dataCount;
/// Create a copy of SpeakingAgregateScore
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpeakingAgregateScoreCopyWith<SpeakingAgregateScore> get copyWith => _$SpeakingAgregateScoreCopyWithImpl<SpeakingAgregateScore>(this as SpeakingAgregateScore, _$identity);

  /// Serializes this SpeakingAgregateScore to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SpeakingAgregateScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&(identical(other.dataCount, dataCount) || other.dataCount == dataCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,dataCount);

@override
String toString() {
  return 'SpeakingAgregateScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, dataCount: $dataCount)';
}


}

/// @nodoc
abstract mixin class $SpeakingAgregateScoreCopyWith<$Res>  {
  factory $SpeakingAgregateScoreCopyWith(SpeakingAgregateScore value, $Res Function(SpeakingAgregateScore) _then) = _$SpeakingAgregateScoreCopyWithImpl;
@useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, int dataCount
});




}
/// @nodoc
class _$SpeakingAgregateScoreCopyWithImpl<$Res>
    implements $SpeakingAgregateScoreCopyWith<$Res> {
  _$SpeakingAgregateScoreCopyWithImpl(this._self, this._then);

  final SpeakingAgregateScore _self;
  final $Res Function(SpeakingAgregateScore) _then;

/// Create a copy of SpeakingAgregateScore
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? dataCount = null,}) {
  return _then(_self.copyWith(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,dataCount: null == dataCount ? _self.dataCount : dataCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [SpeakingAgregateScore].
extension SpeakingAgregateScorePatterns on SpeakingAgregateScore {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SpeakingAgregateScore value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SpeakingAgregateScore() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SpeakingAgregateScore value)  $default,){
final _that = this;
switch (_that) {
case _SpeakingAgregateScore():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SpeakingAgregateScore value)?  $default,){
final _that = this;
switch (_that) {
case _SpeakingAgregateScore() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SpeakingAgregateScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)  $default,) {final _that = this;
switch (_that) {
case _SpeakingAgregateScore():
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double accuracyScore,  double fluencyScore,  double prosodyScore,  double completenessScore,  double pronScore,  int dataCount)?  $default,) {final _that = this;
switch (_that) {
case _SpeakingAgregateScore() when $default != null:
return $default(_that.accuracyScore,_that.fluencyScore,_that.prosodyScore,_that.completenessScore,_that.pronScore,_that.dataCount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SpeakingAgregateScore implements SpeakingAgregateScore {
  const _SpeakingAgregateScore({this.accuracyScore = 0, this.fluencyScore = 0, this.prosodyScore = 0, this.completenessScore = 0, this.pronScore = 0, this.dataCount = 0});
  factory _SpeakingAgregateScore.fromJson(Map<String, dynamic> json) => _$SpeakingAgregateScoreFromJson(json);

@override@JsonKey() final  double accuracyScore;
@override@JsonKey() final  double fluencyScore;
@override@JsonKey() final  double prosodyScore;
@override@JsonKey() final  double completenessScore;
@override@JsonKey() final  double pronScore;
@override@JsonKey() final  int dataCount;

/// Create a copy of SpeakingAgregateScore
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpeakingAgregateScoreCopyWith<_SpeakingAgregateScore> get copyWith => __$SpeakingAgregateScoreCopyWithImpl<_SpeakingAgregateScore>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SpeakingAgregateScoreToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SpeakingAgregateScore&&(identical(other.accuracyScore, accuracyScore) || other.accuracyScore == accuracyScore)&&(identical(other.fluencyScore, fluencyScore) || other.fluencyScore == fluencyScore)&&(identical(other.prosodyScore, prosodyScore) || other.prosodyScore == prosodyScore)&&(identical(other.completenessScore, completenessScore) || other.completenessScore == completenessScore)&&(identical(other.pronScore, pronScore) || other.pronScore == pronScore)&&(identical(other.dataCount, dataCount) || other.dataCount == dataCount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accuracyScore,fluencyScore,prosodyScore,completenessScore,pronScore,dataCount);

@override
String toString() {
  return 'SpeakingAgregateScore(accuracyScore: $accuracyScore, fluencyScore: $fluencyScore, prosodyScore: $prosodyScore, completenessScore: $completenessScore, pronScore: $pronScore, dataCount: $dataCount)';
}


}

/// @nodoc
abstract mixin class _$SpeakingAgregateScoreCopyWith<$Res> implements $SpeakingAgregateScoreCopyWith<$Res> {
  factory _$SpeakingAgregateScoreCopyWith(_SpeakingAgregateScore value, $Res Function(_SpeakingAgregateScore) _then) = __$SpeakingAgregateScoreCopyWithImpl;
@override @useResult
$Res call({
 double accuracyScore, double fluencyScore, double prosodyScore, double completenessScore, double pronScore, int dataCount
});




}
/// @nodoc
class __$SpeakingAgregateScoreCopyWithImpl<$Res>
    implements _$SpeakingAgregateScoreCopyWith<$Res> {
  __$SpeakingAgregateScoreCopyWithImpl(this._self, this._then);

  final _SpeakingAgregateScore _self;
  final $Res Function(_SpeakingAgregateScore) _then;

/// Create a copy of SpeakingAgregateScore
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accuracyScore = null,Object? fluencyScore = null,Object? prosodyScore = null,Object? completenessScore = null,Object? pronScore = null,Object? dataCount = null,}) {
  return _then(_SpeakingAgregateScore(
accuracyScore: null == accuracyScore ? _self.accuracyScore : accuracyScore // ignore: cast_nullable_to_non_nullable
as double,fluencyScore: null == fluencyScore ? _self.fluencyScore : fluencyScore // ignore: cast_nullable_to_non_nullable
as double,prosodyScore: null == prosodyScore ? _self.prosodyScore : prosodyScore // ignore: cast_nullable_to_non_nullable
as double,completenessScore: null == completenessScore ? _self.completenessScore : completenessScore // ignore: cast_nullable_to_non_nullable
as double,pronScore: null == pronScore ? _self.pronScore : pronScore // ignore: cast_nullable_to_non_nullable
as double,dataCount: null == dataCount ? _self.dataCount : dataCount // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$Bookmark {

 String get docId; String get path; bool get isBookmarked;
/// Create a copy of Bookmark
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookmarkCopyWith<Bookmark> get copyWith => _$BookmarkCopyWithImpl<Bookmark>(this as Bookmark, _$identity);

  /// Serializes this Bookmark to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Bookmark&&(identical(other.docId, docId) || other.docId == docId)&&(identical(other.path, path) || other.path == path)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,docId,path,isBookmarked);

@override
String toString() {
  return 'Bookmark(docId: $docId, path: $path, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class $BookmarkCopyWith<$Res>  {
  factory $BookmarkCopyWith(Bookmark value, $Res Function(Bookmark) _then) = _$BookmarkCopyWithImpl;
@useResult
$Res call({
 String docId, String path, bool isBookmarked
});




}
/// @nodoc
class _$BookmarkCopyWithImpl<$Res>
    implements $BookmarkCopyWith<$Res> {
  _$BookmarkCopyWithImpl(this._self, this._then);

  final Bookmark _self;
  final $Res Function(Bookmark) _then;

/// Create a copy of Bookmark
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? docId = null,Object? path = null,Object? isBookmarked = null,}) {
  return _then(_self.copyWith(
docId: null == docId ? _self.docId : docId // ignore: cast_nullable_to_non_nullable
as String,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [Bookmark].
extension BookmarkPatterns on Bookmark {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Bookmark value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Bookmark() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Bookmark value)  $default,){
final _that = this;
switch (_that) {
case _Bookmark():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Bookmark value)?  $default,){
final _that = this;
switch (_that) {
case _Bookmark() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String docId,  String path,  bool isBookmarked)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Bookmark() when $default != null:
return $default(_that.docId,_that.path,_that.isBookmarked);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String docId,  String path,  bool isBookmarked)  $default,) {final _that = this;
switch (_that) {
case _Bookmark():
return $default(_that.docId,_that.path,_that.isBookmarked);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String docId,  String path,  bool isBookmarked)?  $default,) {final _that = this;
switch (_that) {
case _Bookmark() when $default != null:
return $default(_that.docId,_that.path,_that.isBookmarked);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Bookmark implements Bookmark {
  const _Bookmark({required this.docId, required this.path, this.isBookmarked = true});
  factory _Bookmark.fromJson(Map<String, dynamic> json) => _$BookmarkFromJson(json);

@override final  String docId;
@override final  String path;
@override@JsonKey() final  bool isBookmarked;

/// Create a copy of Bookmark
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookmarkCopyWith<_Bookmark> get copyWith => __$BookmarkCopyWithImpl<_Bookmark>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookmarkToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Bookmark&&(identical(other.docId, docId) || other.docId == docId)&&(identical(other.path, path) || other.path == path)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,docId,path,isBookmarked);

@override
String toString() {
  return 'Bookmark(docId: $docId, path: $path, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class _$BookmarkCopyWith<$Res> implements $BookmarkCopyWith<$Res> {
  factory _$BookmarkCopyWith(_Bookmark value, $Res Function(_Bookmark) _then) = __$BookmarkCopyWithImpl;
@override @useResult
$Res call({
 String docId, String path, bool isBookmarked
});




}
/// @nodoc
class __$BookmarkCopyWithImpl<$Res>
    implements _$BookmarkCopyWith<$Res> {
  __$BookmarkCopyWithImpl(this._self, this._then);

  final _Bookmark _self;
  final $Res Function(_Bookmark) _then;

/// Create a copy of Bookmark
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? docId = null,Object? path = null,Object? isBookmarked = null,}) {
  return _then(_Bookmark(
docId: null == docId ? _self.docId : docId // ignore: cast_nullable_to_non_nullable
as String,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
